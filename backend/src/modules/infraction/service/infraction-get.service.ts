import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService } from '../../../common';
import { InfractionService } from '../infraction.service';

@Injectable()
export class InfractionGetService {
  constructor(
    private prismaService: PrismaService,
    private infractionService: InfractionService,
    private paginationService: PaginationUtil,
  ) {}

  // async getAllInfractions(organizationId: string, page = '1', limit = '20') {
  //   const organization = await this.prismaService.organization.findUnique({
  //     where: {
  //       id: organizationId,
  //     },
  //     select: {
  //       name: true,
  //       phone: true,
  //       Children: {
  //         select: {
  //           id: true,
  //           name: true,
  //           Worker: {
  //             select: {
  //               id: true,
  //               fullName: true,
  //               phone: true,
  //               position: {
  //                 select: {
  //                   id: true,
  //                   name: true,
  //                 },
  //               },
  //             },
  //           },
  //           Employee: {
  //             select: {
  //               id: true,
  //               fullName: true,
  //               phone: true,
  //               position: {
  //                 select: {
  //                   id: true,
  //                   name: true,
  //                 },
  //               },
  //             },
  //           },
  //         },
  //       },
  //       UnderControl: {
  //         select: {
  //           id: true,
  //           name: true,
  //           Worker: {
  //             select: {
  //               id: true,
  //               fullName: true,
  //               phone: true,
  //               position: {
  //                 select: {
  //                   id: true,
  //                   name: true,
  //                 },
  //               },
  //             },
  //           },
  //           Employee: {
  //             select: {
  //               id: true,
  //               fullName: true,
  //               phone: true,
  //               position: {
  //                 select: {
  //                   id: true,
  //                   name: true,
  //                 },
  //               },
  //             },
  //           },
  //         },
  //       },
  //       Worker: {
  //         select: {
  //           id: true,
  //           fullName: true,
  //           phone: true,
  //           position: {
  //             select: {
  //               id: true,
  //               name: true,
  //             },
  //           },
  //         },
  //       },
  //       Employee: {
  //         select: {
  //           id: true,
  //           fullName: true,
  //           phone: true,
  //           position: {
  //             select: {
  //               id: true,
  //               name: true,
  //             },
  //           },
  //         },
  //       },
  //     },
  //   });

  //   const $page = Number(page);
  //   const $limit = Number(limit);

  //   if (!organization) {
  //     throw new NotFoundException(`Organization with ID ${organizationId} does not exist`);
  //   }

  //   const allUserIds = [
  //     ...organization.Children.flatMap((child) => child.Worker.map((worker) => worker.id)),
  //     ...organization.Children.flatMap((child) => child.Employee.map((employee) => employee.id)),
  //     ...organization.UnderControl.flatMap((underControl) => underControl.Worker.map((worker) => worker.id)),
  //     ...organization.UnderControl.flatMap((underControl) => underControl.Employee.map((employee) => employee.id)),
  //     ...organization.Worker.map((worker) => worker.id),
  //     ...organization.Employee.map((employee) => employee.id),
  //   ];

  //   const org = this.prismaService.organization;
  //   const model = this.prismaService.infraction;
  //   const withPagination = await this.paginationService.paginate(
  //     model,
  //     {
  //       page: $page,
  //       limit: $limit,
  //     },
  //     {
  //       userId: {
  //         in: allUserIds,
  //       },
  //     },
  //     undefined,
  //     {
  //       createdAt: 'desc',
  //     },
  //     {
  //       id: true,
  //       name: true,
  //       description: true,
  //       userId: true,
  //       data: true,
  //       infractionDate: true,
  //       createdAt: true,
  //       updatedAt: true,
  //     },
  //   );

  //   return withPagination;
  // }

  async getAllInfractions(organizationId: string, page = '1', limit = '20', search?: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: {
        id: organizationId,
      },
      select: {
        Children: {
          select: {
            Worker: { select: { id: true } },
            Employee: { select: { id: true } },
          },
        },
        UnderControl: {
          select: {
            Worker: { select: { id: true } },
            Employee: { select: { id: true } },
          },
        },
        Worker: { select: { id: true } },
        Employee: { select: { id: true } },
      },
    });

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${organizationId} does not exist`);
    }

    const allUserIds = [
      ...organization.Children.flatMap((c) => c.Worker.map((w) => w.id)),
      ...organization.Children.flatMap((c) => c.Employee.map((e) => e.id)),
      ...organization.UnderControl.flatMap((uc) => uc.Worker.map((w) => w.id)),
      ...organization.UnderControl.flatMap((uc) => uc.Employee.map((e) => e.id)),
      ...organization.Worker.map((w) => w.id),
      ...organization.Employee.map((e) => e.id),
    ];

    const $page = Number(page);
    const $limit = Number(limit);
    const skip = ($page - 1) * $limit;

    const [data, total] = await Promise.all([
      this.prismaService.infraction.findMany({
        where: {
          userId: {
            in: allUserIds,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: $limit,
        select: {
          id: true,
          name: true,
          description: true,
          // userId: true,
          data: true,
          infractionDate: true,
          createdAt: true,
          updatedAt: true,
          user: {
            select: {
              id: true,
              fullName: true,
              position: {
                select: {
                  id: true,
                  name: true,
                  type: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
              Organization: {
                select: {
                  name: true,
                },
              },
              MainOrganization: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      }),

      this.prismaService.infraction.count({
        where: {
          userId: {
            in: allUserIds,
          },
        },
      }),
    ]);

    return {
      data,
      total,
      page: $page,
      limit: $limit,
      totalPages: Math.ceil(total / $limit),
    };
  }

  async getByUserId(userId: string) {
    const infraction = await this.prismaService.infraction.findMany({
      where: { userId },
      include: {
        InfractionReason: {
          include: {
            file: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (!infraction) {
      throw new NotFoundException('Infraction topilmadi');
    }

    return infraction;
  }
}
